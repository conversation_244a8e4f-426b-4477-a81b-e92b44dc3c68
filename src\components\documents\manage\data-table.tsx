// components/documents/manage/data-table.tsx
"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Plus, Search, X } from "lucide-react";
import * as React from "react";
import { DateRange } from "react-day-picker";
import { ColumnFilter } from "../view/column-filter";
import { PaginationControls } from "../view/pagination-controls";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  title?: string;
  description?: string;
  onAddNew?: () => void;
}

// Custom filter function for date range
const dateRangeFilter: FilterFn<any> = (
  row: any,
  columnId: string,
  filterValue: DateRange
) => {
  if (!filterValue?.from) return true;

  const cellValue = row.getValue(columnId);
  if (!cellValue) return false;

  const date = new Date(cellValue);
  const from = filterValue.from;
  const to = filterValue.to || filterValue.from;

  return date >= from && date <= to;
};

// Custom filter function for visibility column
const visibilityFilter: FilterFn<any> = (
  row: any,
  columnId: string,
  filterValue: string
) => {
  if (!filterValue || filterValue === "all") return true;

  const cellValue = row.getValue(columnId);
  const booleanValue = Boolean(cellValue);

  return filterValue === "true" ? booleanValue : !booleanValue;
};

// Custom filter function for days until revision (numeric string matching)
const daysUntilRevisionFilter: FilterFn<any> = (
  row: any,
  columnId: string,
  filterValue: string
) => {
  if (!filterValue) return true;

  const cellValue = row.getValue(columnId);
  if (cellValue === null || cellValue === undefined) return false;

  // Convert the numeric value to string and check if it includes the filter value
  const stringValue = String(cellValue);
  return stringValue.includes(filterValue);
};

export function ManageDataTable<TData, TValue>({
  columns,
  data,
  title = "Manage Documents",
  description = "Manage your document library with full CRUD operations",
  onAddNew,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([
    { desc: false, id: "document" },
  ]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = React.useState("");

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    filterFns: {
      dateRange: dateRangeFilter,
      visibility: visibilityFilter,
      daysUntilRevision: daysUntilRevisionFilter,
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between flex-wrap w-full gap-4">
            <div className="flex items-center space-x-2 flex-wrap">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search all columns..."
                  value={globalFilter ?? ""}
                  onChange={(event) => setGlobalFilter(event.target.value)}
                  className="pl-8 w-sm"
                />
              </div>
              {(table.getState().columnFilters.length > 0 || globalFilter) && (
                <Button
                  variant="ghost"
                  onClick={() => {
                    table.resetColumnFilters();
                    setGlobalFilter("");
                  }}
                  className="h-8 px-2 lg:px-3"
                >
                  Reset
                  <X className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-4 flex-wrap">
              {onAddNew && (
                <Button onClick={onAddNew} className="h-8 px-3">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Document
                </Button>
              )}
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id} className="px-4 py-3">
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
                {/* Filter Row */}
                <TableRow className="bg-muted/30">
                  {table.getFlatHeaders().map((header) => {
                    const column = header.column;
                    return (
                      <TableHead key={header.id} className="px-4 py-2">
                        {column.getCanFilter() ? (
                          <ColumnFilter
                            column={column}
                            title={column.id.replace("_", " ")}
                          />
                        ) : null}
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-muted/50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="px-4 py-3">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <PaginationControls table={table} />
        </div>
      </CardContent>
    </Card>
  );
}
