export const GET_VIEW_DOCUMENTS = `
query GetViewDocuments {
  documents(where: {_and: {company: {_eq: "GS"}, visibile: {_eq: true}}}, order_by: {document: desc_nulls_last}) {
    id
    document
    department
    language
    link
    release_date
    company
  }
}
`;

// GraphQL query for getting all documents for management
export const GET_MANAGE_DOCUMENTS = `
  query GetManageDocuments {
    documents(
      where: {_and: {company: {_eq: "GS"}}}
      order_by: {document: desc_nulls_last}
    ) {
      id
      tags
      document
      department
      language
      link
      release_date
      revision_date
      days_until_revision
      visibile
      view_counter
      company
    }
  }
`;
