// app/api/documents/download/route.ts
import { NextRequest, NextResponse } from "next/server";
import { readFile, access, constants } from "fs/promises";
import {
  convertWindowsPathToLinux,
  convertToGSDocsPath,
  getFileNameFromPath,
  getMimeType,
} from "@/lib/file-utils";
import { getSession } from "@/lib/auth";

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user || !session.isGS) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { filePath } = body;

    if (!filePath) {
      return NextResponse.json(
        { error: "File path is required" },
        { status: 400 }
      );
    }

    // Try to serve from gsdocs folder first, fallback to original path
    const fileName = getFileNameFromPath(filePath);
    const gsDocsPath = convertToGSDocsPath(filePath);
    const containerPath = convertWindowsPathToLinux(filePath);

    console.log(`Attempting to download from gsdocs: ${gsDocsPath}`);
    console.log(`Fallback path: ${containerPath}`);

    let finalPath = gsDocsPath;

    // Check if file exists in gsdocs folder first, then fallback to original path
    try {
      await access(gsDocsPath, constants.F_OK);
      finalPath = gsDocsPath;
    } catch (error) {
      console.log(
        `File not found in gsdocs, trying original path: ${containerPath}`
      );
      try {
        await access(containerPath, constants.F_OK);
        finalPath = containerPath;
      } catch (fallbackError) {
        console.error(
          `File not found in both locations: ${gsDocsPath}, ${containerPath}`,
          fallbackError
        );
        return NextResponse.json({ error: "File not found" }, { status: 404 });
      }
    }

    const fileBuffer = await readFile(finalPath);

    // Note: View counter is now updated in the frontend hook before download
    // to ensure consistent counting for both URL links and file downloads

    const mimeType = getMimeType(fileName);

    const response = new NextResponse(fileBuffer as any);

    response.headers.set("Content-Type", mimeType);
    response.headers.set(
      "Content-Disposition",
      `attachment; filename="${fileName}"`
    );
    response.headers.set("Content-Length", fileBuffer.length.toString());

    return response;
  } catch (error) {
    console.error("Download error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
